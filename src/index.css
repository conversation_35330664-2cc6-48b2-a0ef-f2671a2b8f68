
@import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=Inter:wght@400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 98%;
    --foreground: 0 0% 10%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 10%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 10%;

    --primary: 47 100% 50%;
    --primary-foreground: 0 0% 10%;

    --secondary: 196 100% 50%;
    --secondary-foreground: 0 0% 100%;

    --accent: 339 100% 60%;
    --accent-foreground: 0 0% 100%;

    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 45%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 0%;
    --input: 0 0% 0%;
    --ring: 0 0% 0%;

    --radius: 0rem;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    overflow-x: hidden;
  }

  .neo-border {
    @apply border-[3px] border-black;
  }

  .neo-shadow {
    @apply shadow-[4px_4px_0px_0px_rgba(0,0,0,1)];
  }

  .neo-card {
    @apply neo-border neo-shadow bg-white transition-all duration-300;
  }

  .neo-button {
    @apply neo-border neo-shadow px-6 py-3 font-bold transition-all duration-200 active:translate-x-[2px] active:translate-y-[2px] active:shadow-[2px_2px_0px_0px_rgba(0,0,0,1)];
  }

  .neo-input {
    @apply neo-border bg-white px-4 py-3 focus:outline-none focus:ring-4 ring-primary/50;
  }

  .step-enter {
    @apply opacity-0 translate-x-4;
  }

  .step-enter-active {
    @apply opacity-100 translate-x-0 transition-all duration-300;
  }

  .step-exit {
    @apply opacity-100 translate-x-0;
  }

  .step-exit-active {
    @apply opacity-0 -translate-x-4 transition-all duration-300;
  }
}
