
/**
 * Service to handle external redirections after form submission
 */

import { supabase } from '../integrations/supabase/client';

interface ClientData {
  firstName: string;
  lastName: string;
  email?: string;
  companyName: string;
  website: string;
  businessType?: string;
  fundingStatus?: string;
  businessStage?: string;
  industry?: string;
  otherIndustry?: string;
  runningAds?: string;
  adsEffective?: string;
  adBudget?: string;
  marketingManagement?: string;
  marketingAreas?: string[];
  decisionMakers?: string[];
  implementationTimeline?: string;
  marketingChallenges?: string;
  sessionOutcomes?: string;
  // Add any other relevant data fields from the form
}

export const redirectToHumainMarketing = async (clientData: ClientData): Promise<void> => {
  try {
    // Create user account on the Humain platform
    if (clientData.email) {
      // First check if the user already exists in the system
      const { data: existingData, error: checkError } = await supabase
        .from('leads')
        .select('id, email, converted_to_customer')
        .eq('email', clientData.email)
        .eq('converted_to_customer', true);
        
      if (checkError) {
        console.error('Error checking existing users:', checkError);
      } else if (!existingData || existingData.length === 0) {
        // For now, let's assume the user creation happens on the Humain platform
        // We just mark them as converted to customer in our database
        console.log('Creating new user account for:', clientData.email);
      } else {
        console.log('User already exists, proceeding with redirection');
      }
    }
    
    // In a real implementation, this would be an API call to humain.marketing
    // to create a new client account with the provided data
    console.log('Submitting client data to humain.marketing:', clientData);
    
    // For now, we'll simulate the API call with a timeout
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Build a query string with client data for the registration page
    const queryParams = new URLSearchParams();
    queryParams.append('client', encodeURIComponent(clientData.companyName));
    queryParams.append('source', 'therapy');
    
    // Add email for pre-filling the registration form
    if (clientData.email) {
      queryParams.append('email', encodeURIComponent(clientData.email));
    }
    
    // Add full name for pre-filling
    if (clientData.firstName && clientData.lastName) {
      queryParams.append('name', encodeURIComponent(`${clientData.firstName} ${clientData.lastName}`));
    }
    
    // Add important marketing areas if available
    if (clientData.marketingAreas && clientData.marketingAreas.length > 0) {
      queryParams.append('areas', clientData.marketingAreas.join(','));
    }
    
    // Add business type if available
    if (clientData.businessType) {
      queryParams.append('type', clientData.businessType);
    }
    
    // Add marketing challenges from the form
    if (clientData.marketingChallenges) {
      queryParams.append('challenges', encodeURIComponent(clientData.marketingChallenges));
    }
    
    // Redirect to humain.marketing registration page with client data
    window.location.href = `https://humain.marketing/register?${queryParams.toString()}`;
  } catch (error) {
    console.error('Error redirecting to humain.marketing:', error);
    throw error;
  }
};
