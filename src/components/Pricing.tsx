
import React from 'react';
import { Check } from 'lucide-react';

interface PricingProps {
  onBookingClick: () => void;
}

const Pricing: React.FC<PricingProps> = ({ onBookingClick }) => {
  const features = [
    "30-minute one-on-one session",
    "Expert marketing advice",
    "Personalized recommendations",
    "Follow-up action plan",
    "Access to resource library"
  ];

  return (
    <section id="pricing" className="py-20 overflow-hidden">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <span className="inline-block px-4 py-2 neo-border bg-yellow text-black font-bold mb-4 -rotate-1">
            Simple Pricing
          </span>
          <h2 className="text-3xl md:text-5xl font-bold">
            Marketing Therapy Session
          </h2>
        </div>
        
        <div className="max-w-2xl mx-auto">
          <div className="neo-card p-8 md:p-12 bg-white relative overflow-hidden">
            {/* Price tag */}
            <div className="absolute -top-5 -right-5 w-32 h-32">
              <div className="absolute top-0 right-0 w-full h-full bg-accent neo-border rotate-12 flex flex-col items-center justify-center text-white">
                <span className="text-xl font-bold">$199</span>
                <span className="text-sm">per session</span>
              </div>
            </div>
            
            <h3 className="text-2xl font-bold mb-6">30-Minute Marketing Therapy</h3>
            <p className="text-lg mb-8">
              Sometimes you just need to talk with someone who gets it. Get the clarity and guidance 
              you need to overcome your marketing challenges.
            </p>
            
            <div className="mb-10">
              <ul className="space-y-4">
                {features.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <span className="mr-3 text-accent">
                      <Check size={24} />
                    </span>
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
            
            <button 
              onClick={onBookingClick}
              className="w-full neo-button bg-primary hover:bg-primary/90 text-center text-xl"
            >
              Book Your Session
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Pricing;
