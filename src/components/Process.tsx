
import React from 'react';

const Process = () => {
  const steps = [
    {
      number: "01",
      title: "Book a Session",
      description: "Select a time that works for you and tell us about your marketing challenges."
    },
    {
      number: "02",
      title: "Meet Your Therapist",
      description: "Join a video call with a marketing expert who specializes in your specific issues."
    },
    {
      number: "03",
      title: "Get Actionable Advice",
      description: "Walk away with clear next steps and strategic guidance you can implement right away."
    }
  ];

  return (
    <section id="process" className="py-20 overflow-hidden">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <span className="inline-block px-4 py-2 neo-border bg-pink text-white font-bold mb-4 -rotate-1">
            Simple Process
          </span>
          <h2 className="text-3xl md:text-5xl font-bold">
            How It Works
          </h2>
        </div>
        
        <div className="relative">
          {/* Connection line */}
          <div className="hidden md:block absolute top-1/2 left-0 right-0 h-1 bg-black transform -translate-y-1/2 z-0"></div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 relative z-10">
            {steps.map((step, index) => (
              <div 
                key={index}
                className="flex flex-col items-center text-center"
              >
                <div 
                  className="w-20 h-20 flex items-center justify-center neo-border bg-white text-2xl font-bold mb-6"
                  style={{ transform: `rotate(${index % 2 === 0 ? '3' : '-3'}deg)` }}
                >
                  {step.number}
                </div>
                <h3 className="text-xl font-bold mb-3">{step.title}</h3>
                <p>{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Process;
