import React, { useState, useEffect } from 'react';
import { useFormContext } from '../../contexts/FormContext';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { useToast } from '../../hooks/use-toast';
import { Loader2 } from 'lucide-react';
import { supabase } from '../../integrations/supabase/client';
import WebsitePreview, { WebsiteMetadata } from './WebsitePreview';
import { 
  fetchWebsiteMetadata, 
  normalizeUrl, 
  analyzeWebsiteWithAI 
} from '../../services/websiteMetadataService';
import { Alert, AlertTitle, AlertDescription } from '../ui/alert';
import { Skeleton } from '@/components/ui/skeleton';

const StepOne = () => {
  const { formData, updateFormData, errors, leadId } = useFormContext();
  const { toast } = useToast();
  const [isScrapingWebsite, setIsScrapingWebsite] = useState(false);
  const [isCheckingEmail, setIsCheckingEmail] = useState(false);
  const [websiteMetadata, setWebsiteMetadata] = useState<WebsiteMetadata | null>(null);
  const [metadataError, setMetadataError] = useState<string>('');
  const [isLoadingMetadata, setIsLoadingMetadata] = useState(false);
  const [websiteDebounce, setWebsiteDebounce] = useState<NodeJS.Timeout | null>(null);
  const [isMetadataRetrying, setIsMetadataRetrying] = useState(false);
  
  // Track fields analyzed by AI
  const [analyzedFields, setAnalyzedFields] = useState<{
    companyName: boolean;
    industry: boolean;
  }>({
    companyName: false,
    industry: false
  });
  
  // Hide form fields until website analysis is complete
  const [showFields, setShowFields] = useState<boolean>(true);

  const handleChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    updateFormData({ [name]: value });

    // If the website field is updated and has a value, check if we should analyze it
    if (name === 'website') {
      // Clear previous metadata when the URL changes
      if (websiteDebounce) clearTimeout(websiteDebounce);
      setWebsiteMetadata(null);
      setMetadataError('');
      setShowFields(false);
      setAnalyzedFields({ companyName: false, industry: false });
      
      if (value.trim().length > 5) {
        // Set a shorter debounce to avoid too many requests while typing
        const debounceTimeout = setTimeout(() => {
          fetchMetadata(value);
        }, 600); // Slight increase to reduce requests during typing
        setWebsiteDebounce(debounceTimeout);
      } else {
        setWebsiteMetadata(null);
        setMetadataError('');
        setShowFields(true);
      }
    }
    
    // If email is updated and there's no leadId yet, check if we have this lead already
    if (name === 'email' && value.trim().length > 5 && !leadId) {
      await checkExistingLead(value);
    }
  };

  const fetchMetadata = async (url: string) => {
    if (!url || url.length < 5) return;
    
    setIsLoadingMetadata(true);
    setMetadataError('');
    
    try {
      // Show toast to let user know we're fetching
      toast({
        title: "Fetching website info",
        description: "Getting preview information...",
        duration: 3000,
      });
      
      const metadata = await fetchWebsiteMetadata(url);
      setWebsiteMetadata(metadata);
      setIsMetadataRetrying(false);
      
      // If successful and we got data, show success toast
      if (metadata.title) {
        toast({
          title: "Website found",
          description: "Website preview loaded successfully.",
          duration: 3000,
        });
        
        // Now analyze the website with AI
        analyzeWebsite(url);
      }
    } catch (error) {
      console.error('Error fetching metadata:', error);
      const errorMessage = (error as Error).message || 'Failed to fetch website information';
      setMetadataError(errorMessage);
      
      toast({
        title: "Website preview failed",
        description: errorMessage,
        variant: "destructive",
        duration: 5000,
      });
      
      setWebsiteMetadata(null);
      setShowFields(true); // Show fields if metadata fetch fails
    } finally {
      setIsLoadingMetadata(false);
    }
  };

  // Try once more if we initially failed
  const retryFetchMetadata = () => {
    if (formData.website && !isLoadingMetadata && metadataError) {
      setIsMetadataRetrying(true);
      fetchMetadata(formData.website);
    }
  };

  // If website is already filled on component mount, fetch metadata
  useEffect(() => {
    if (formData.website && formData.website.trim().length > 5) {
      fetchMetadata(formData.website);
    } else {
      setShowFields(true);
    }
  }, []);

  const checkExistingLead = async (email: string) => {
    if (!email.includes('@') || isCheckingEmail) return;
    
    setIsCheckingEmail(true);
    
    try {
      const { data, error } = await supabase
        .from('leads')
        .select('*')
        .eq('email', email)
        .order('created_at', { ascending: false })
        .limit(1);
        
      if (error) {
        console.error('Error checking for existing lead:', error);
        return;
      }
      
      if (data && data.length > 0) {
        const lead = data[0];
        
        // Populate form with existing lead data
        updateFormData({
          firstName: lead.first_name || '',
          lastName: lead.last_name || '',
          companyName: lead.company_name || '',
          website: lead.website || '',
          // We don't need to update email as it's already set
        });
        
        toast({
          title: "Welcome back!",
          description: "We've restored your previous information.",
        });
        
        // Store the lead ID in localStorage
        localStorage.setItem('leadId', lead.id);
      }
    } catch (err) {
      console.error('Error checking for existing lead:', err);
    } finally {
      setIsCheckingEmail(false);
    }
  };

  const analyzeWebsite = async (url: string) => {
    // Only proceed if URL is reasonably valid
    if (!url.includes('.')) return;
    
    // Normalize URL
    let normalizedUrl = normalizeUrl(url);

    try {
      setIsScrapingWebsite(true);
      setShowFields(false); // Hide fields while analyzing
      
      toast({
        title: "Analyzing your website",
        description: "Please wait while we gather information using AI...",
      });
      
      try {
        // Use OpenAI to analyze the website
        const analysisResults = await analyzeWebsiteWithAI(normalizedUrl);
        
        if (analysisResults) {
          // Update form data with AI analysis
          updateFormData({ 
            companyName: analysisResults.companyName || '',
            industry: analysisResults.industry || '',
            // You can add more fields here as needed
          });
          
          setAnalyzedFields({
            companyName: !!analysisResults.companyName,
            industry: !!analysisResults.industry
          });
          
          toast({
            title: "Analysis complete",
            description: `We've analyzed your website and filled out some information for you.`,
          });
        }
      } catch (error) {
        console.error('Error in AI analysis:', error);
        // Fallback to basic extraction if AI fails
        
        // Extract domain as company name for fallback
        const domain = new URL(normalizedUrl).hostname.replace('www.', '').split('.')[0];
        const companyName = domain.charAt(0).toUpperCase() + domain.slice(1);
        
        updateFormData({ 
          companyName: companyName,
        });
        
        setAnalyzedFields({
          companyName: true,
          industry: false
        });
        
        toast({
          title: "Basic analysis complete",
          description: `We've extracted some basic information from your website.`,
        });
      }
    } catch (error) {
      console.error('Error analyzing website:', error);
      toast({
        title: "Website analysis failed",
        description: "We couldn't analyze your website. Please fill in the information manually.",
        variant: "destructive"
      });
    } finally {
      setIsScrapingWebsite(false);
      setShowFields(true); // Show fields after analysis is complete
    }
  };

  return (
    <div className="animate-fade-in">
      <h2 className="text-2xl font-bold mb-2">30-Minute Marketing Therapy</h2>
      <p className="mb-6">Sometimes you just need to talk!</p>
      
      <div className="space-y-4">
        <div>
          <Label htmlFor="firstName" className="block mb-1 font-medium">
            First Name
          </Label>
          <Input 
            type="text"
            id="firstName"
            name="firstName"
            value={formData.firstName}
            onChange={handleChange}
            className={`neo-input w-full ${errors.firstName ? 'border-red-500' : ''}`}
          />
          {errors.firstName && (
            <p className="mt-1 text-red-500 text-sm">{errors.firstName}</p>
          )}
        </div>
        
        <div>
          <Label htmlFor="lastName" className="block mb-1 font-medium">
            Last Name
          </Label>
          <Input 
            type="text"
            id="lastName"
            name="lastName"
            value={formData.lastName}
            onChange={handleChange}
            className={`neo-input w-full ${errors.lastName ? 'border-red-500' : ''}`}
          />
          {errors.lastName && (
            <p className="mt-1 text-red-500 text-sm">{errors.lastName}</p>
          )}
        </div>
        
        <div>
          <Label htmlFor="email" className="block mb-1 font-medium">
            Email
          </Label>
          <div className="relative">
            <Input 
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              className={`neo-input w-full ${errors.email ? 'border-red-500' : ''}`}
            />
            {isCheckingEmail && (
              <Loader2 className="animate-spin h-4 w-4 absolute right-3 top-3 text-primary" />
            )}
          </div>
          {errors.email && (
            <p className="mt-1 text-red-500 text-sm">{errors.email}</p>
          )}
        </div>
        
        <div>
          <Label htmlFor="website" className="block mb-1 font-medium">
            Company Website
          </Label>
          <div className="relative">
            <Input 
              type="text"
              id="website"
              name="website"
              value={formData.website}
              onChange={handleChange}
              placeholder="https://example.com"
              className={`neo-input w-full ${errors.website ? 'border-red-500' : ''}`}
            />
            {isScrapingWebsite && (
              <Loader2 className="animate-spin h-4 w-4 absolute right-3 top-3 text-primary" />
            )}
          </div>
          {errors.website && (
            <p className="mt-1 text-red-500 text-sm">{errors.website}</p>
          )}
          <p className="mt-1 text-xs text-muted-foreground">
            Enter your website and we'll analyze it to help pre-fill some information.
          </p>
          
          {metadataError && !isLoadingMetadata && !isMetadataRetrying && (
            <Alert variant="destructive" className="mt-2">
              <AlertTitle>Unable to load preview</AlertTitle>
              <AlertDescription className="flex items-center justify-between">
                <span className="text-sm">{metadataError}</span>
                <button 
                  onClick={retryFetchMetadata}
                  className="text-sm underline hover:no-underline"
                >
                  Retry
                </button>
              </AlertDescription>
            </Alert>
          )}
          
          <WebsitePreview 
            metadata={websiteMetadata}
            isLoading={isLoadingMetadata || isMetadataRetrying}
            error={metadataError}
          />
        </div>
        
        {/* Only show company fields after website analysis is complete */}
        {showFields && (
          <div className={`space-y-4 ${isScrapingWebsite ? 'opacity-50 pointer-events-none' : ''}`}>
            <div>
              <Label htmlFor="companyName" className="block mb-1 font-medium flex items-center">
                Company Name
                {analyzedFields.companyName && (
                  <span className="ml-2 text-xs bg-primary/20 text-primary px-2 py-0.5 rounded-full">
                    Auto-filled
                  </span>
                )}
              </Label>
              <Input 
                type="text"
                id="companyName"
                name="companyName"
                value={formData.companyName}
                onChange={handleChange}
                className={`neo-input w-full ${errors.companyName ? 'border-red-500' : ''}`}
              />
              {errors.companyName && (
                <p className="mt-1 text-red-500 text-sm">{errors.companyName}</p>
              )}
            </div>
            
            {analyzedFields.industry && (
              <div>
                <Label htmlFor="industry" className="block mb-1 font-medium flex items-center">
                  Industry
                  <span className="ml-2 text-xs bg-primary/20 text-primary px-2 py-0.5 rounded-full">
                    Auto-filled
                  </span>
                </Label>
                <Input 
                  type="text"
                  id="industry"
                  name="industry"
                  value={formData.industry}
                  onChange={handleChange}
                  className="neo-input w-full"
                />
              </div>
            )}
          </div>
        )}
        
        {/* Show skeleton loaders while analyzing */}
        {isScrapingWebsite && !showFields && (
          <div className="space-y-4 mt-6">
            <div>
              <Skeleton className="h-5 w-32 mb-2" />
              <Skeleton className="h-10 w-full" />
            </div>
            <div>
              <Skeleton className="h-5 w-24 mb-2" />
              <Skeleton className="h-10 w-full" />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default StepOne;
