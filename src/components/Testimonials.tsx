
import React from 'react';

const Testimonials = () => {
  const testimonials = [
    {
      quote: "The marketing therapy session helped me break through a massive roadblock in our content strategy. Now we're seeing 3x engagement!",
      author: "<PERSON>",
      position: "Marketing Director",
      company: "Tech Startup"
    },
    {
      quote: "The clarity I got in just 30 minutes saved us from wasting thousands on the wrong advertising channels.",
      author: "<PERSON>",
      position: "CEO",
      company: "E-commerce Brand"
    },
    {
      quote: "I was skeptical at first, but the actionable advice I received completely transformed our approach to customer acquisition.",
      author: "<PERSON>",
      position: "CMO",
      company: "SaaS Company"
    }
  ];

  return (
    <section id="testimonials" className="py-20 bg-muted overflow-hidden">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <span className="inline-block px-4 py-2 neo-border bg-blue text-white font-bold mb-4 rotate-1">
            Success Stories
          </span>
          <h2 className="text-3xl md:text-5xl font-bold">
            What Our Clients Say
          </h2>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div 
              key={index}
              className="neo-card p-6 bg-white relative"
              style={{ 
                transform: `rotate(${index % 2 === 0 ? '1' : '-1'}deg)`
              }}
            >
              {/* Quote mark */}
              <div className="absolute -top-5 -right-2 w-12 h-12 flex items-center justify-center neo-border bg-primary text-3xl font-serif rotate-12">
                "
              </div>
              
              <p className="mb-6 text-lg">{testimonial.quote}</p>
              
              <div>
                <p className="font-bold">{testimonial.author}</p>
                <p className="text-sm">{testimonial.position}, {testimonial.company}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
