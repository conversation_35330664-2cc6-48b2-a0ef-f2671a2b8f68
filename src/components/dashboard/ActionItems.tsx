
import React, { useState } from 'react';
import { CheckCircle2, Circle, ChevronUp, ChevronDown } from 'lucide-react';

interface ActionItem {
  id: string;
  title: string;
  completed: boolean;
  priority: 'high' | 'medium' | 'low';
}

interface ActionItemsProps {
  actionItems: ActionItem[];
}

const ActionItems = ({ actionItems: initialItems }: ActionItemsProps) => {
  const [actionItems, setActionItems] = useState<ActionItem[]>(initialItems);
  const [showCompleted, setShowCompleted] = useState(true);
  
  const toggleItemCompletion = (id: string) => {
    setActionItems(items => 
      items.map(item => 
        item.id === id ? { ...item, completed: !item.completed } : item
      )
    );
  };
  
  const incompleteTasks = actionItems.filter(item => !item.completed);
  const completedTasks = actionItems.filter(item => item.completed);
  
  return (
    <div className="neo-card p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-bold">Action Items</h2>
        <span className="text-sm text-muted-foreground">
          {incompleteTasks.length} remaining
        </span>
      </div>
      
      <div className="space-y-3">
        {incompleteTasks.map(item => (
          <div 
            key={item.id} 
            className="flex items-center p-3 neo-border"
          >
            <button 
              onClick={() => toggleItemCompletion(item.id)}
              className="mr-3 flex-shrink-0"
            >
              <Circle className="h-5 w-5 text-muted-foreground" />
            </button>
            
            <div className="flex-grow">
              <p>{item.title}</p>
            </div>
            
            <span className={`text-xs px-2 py-1 rounded-full ${
              item.priority === 'high' 
                ? 'bg-red-100 text-red-800' 
                : item.priority === 'medium'
                  ? 'bg-yellow-100 text-yellow-800'
                  : 'bg-green-100 text-green-800'
            }`}>
              {item.priority}
            </span>
          </div>
        ))}
      </div>
      
      {completedTasks.length > 0 && (
        <>
          <button 
            className="flex items-center mt-4 text-sm text-muted-foreground"
            onClick={() => setShowCompleted(!showCompleted)}
          >
            {showCompleted ? (
              <>
                <ChevronUp size={16} className="mr-1" />
                Hide completed 
              </>
            ) : (
              <>
                <ChevronDown size={16} className="mr-1" />
                Show completed ({completedTasks.length})
              </>
            )}
          </button>
          
          {showCompleted && (
            <div className="mt-3 space-y-3">
              {completedTasks.map(item => (
                <div 
                  key={item.id} 
                  className="flex items-center p-3 neo-border bg-muted/50"
                >
                  <button 
                    onClick={() => toggleItemCompletion(item.id)}
                    className="mr-3 flex-shrink-0"
                  >
                    <CheckCircle2 className="h-5 w-5 text-primary" />
                  </button>
                  
                  <div className="flex-grow">
                    <p className="line-through text-muted-foreground">{item.title}</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default ActionItems;
