
import React from 'react';
import { Calendar, Clock, Building } from 'lucide-react';

interface SessionSummaryProps {
  sessionDate: string;
  company: string;
  topics: string[];
}

const SessionSummary = ({ sessionDate, company, topics }: SessionSummaryProps) => {
  // Format date for display
  const formattedDate = new Date(sessionDate).toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
  
  return (
    <div className="neo-card p-6">
      <h2 className="text-xl font-bold mb-4">Session Summary</h2>
      
      <div className="space-y-4">
        <div className="flex items-center gap-3">
          <div className="h-10 w-10 rounded-full neo-border flex items-center justify-center bg-primary/10">
            <Calendar size={20} className="text-primary" />
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Session Date</p>
            <p className="font-medium">{formattedDate}</p>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <div className="h-10 w-10 rounded-full neo-border flex items-center justify-center bg-primary/10">
            <Clock size={20} className="text-primary" />
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Session Duration</p>
            <p className="font-medium">30 Minutes</p>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <div className="h-10 w-10 rounded-full neo-border flex items-center justify-center bg-primary/10">
            <Building size={20} className="text-primary" />
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Company</p>
            <p className="font-medium">{company}</p>
          </div>
        </div>
        
        <div>
          <p className="text-sm text-muted-foreground mb-2">Topics Covered</p>
          <div className="flex flex-wrap gap-2">
            {topics.map((topic, index) => (
              <span key={index} className="px-3 py-1 neo-border bg-primary/10 text-sm">
                {topic}
              </span>
            ))}
          </div>
        </div>
        
        <div className="mt-6 pt-6 border-t neo-border">
          <h3 className="font-medium mb-2">Key Takeaways</h3>
          <ul className="space-y-2 list-disc pl-5">
            <li>Focus on improving ad campaign structure for better targeting</li>
            <li>Update landing page CTAs to align with customer journey</li>
            <li>Implement proper tracking to measure campaign success</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default SessionSummary;
