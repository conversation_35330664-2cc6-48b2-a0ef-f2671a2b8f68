
import React from 'react';
import { FileText, ExternalLink, Download } from 'lucide-react';

interface Resource {
  id: string;
  title: string;
  type: 'template' | 'guide' | 'tool';
  url: string;
}

interface ResourceLibraryProps {
  resources: Resource[];
}

const ResourceLibrary = ({ resources }: ResourceLibraryProps) => {
  const getIconForType = (type: string) => {
    switch (type) {
      case 'template':
        return <FileText size={16} />;
      case 'guide':
        return <FileText size={16} />;
      case 'tool':
        return <ExternalLink size={16} />;
      default:
        return <FileText size={16} />;
    }
  };
  
  return (
    <div className="neo-card p-6">
      <h2 className="text-xl font-bold mb-4">Resources</h2>
      
      <div className="space-y-3">
        {resources.map(resource => (
          <a 
            key={resource.id}
            href={resource.url}
            className="block p-4 neo-border hover:bg-muted/20 transition-colors"
            target="_blank"
            rel="noopener noreferrer"
          >
            <div className="flex items-start">
              <div className="mr-3 mt-1">
                {getIconForType(resource.type)}
              </div>
              <div className="flex-grow">
                <p className="font-medium">{resource.title}</p>
                <p className="text-sm text-muted-foreground capitalize mt-1">
                  {resource.type}
                </p>
              </div>
              <Download size={16} className="text-primary" />
            </div>
          </a>
        ))}
      </div>
      
      <button className="w-full mt-4 p-2 neo-border hover:bg-muted/20 transition-colors text-sm">
        View All Resources
      </button>
    </div>
  );
};

export default ResourceLibrary;
