
import React from 'react';
import { Calendar, Clock } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

const FollowupBooking = () => {
  return (
    <Card className="neo-card overflow-hidden">
      <CardContent className="p-6">
        <h2 className="text-xl font-bold mb-4">Follow-Up Session</h2>
        
        <div className="mb-6">
          <p className="text-sm mb-4">
            Ready for another therapy session? Schedule your follow-up to track progress and get further guidance.
          </p>
          
          <div className="space-y-3 mb-6">
            <div className="flex items-center gap-3">
              <Calendar size={18} className="text-primary" />
              <p className="text-sm">Recommended: 2 weeks after implementation</p>
            </div>
            
            <div className="flex items-center gap-3">
              <Clock size={18} className="text-primary" />
              <p className="text-sm">30 or 60 minute options available</p>
            </div>
          </div>
          
          <Button className="w-full">
            Schedule Follow-Up
          </Button>
        </div>
        
        <div className="mt-6 pt-4 border-t">
          <h3 className="font-medium text-sm mb-2">Need additional help?</h3>
          <div className="space-y-2">
            <a 
              href="#" 
              className="block text-sm text-primary"
            >
              View ongoing service packages
            </a>
            <a 
              href="/booking" 
              className="block text-sm text-primary"
            >
              Book a different type of session
            </a>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default FollowupBooking;
