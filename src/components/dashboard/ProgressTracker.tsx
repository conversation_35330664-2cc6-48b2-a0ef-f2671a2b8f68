
import React from 'react';
import { Progress } from '@/components/ui/progress';

const ProgressTracker = () => {
  // This would ideally come from an API
  const progressData = {
    overallProgress: 40,
    categorizedProgress: [
      { category: 'Ad Campaign Structure', progress: 65 },
      { category: 'Landing Page Optimization', progress: 80 },
      { category: 'Conversion Tracking', progress: 20 },
      { category: 'Audience Targeting', progress: 15 },
    ]
  };
  
  return (
    <div className="neo-card p-6">
      <h2 className="text-xl font-bold mb-4">Progress Tracker</h2>
      
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <p className="font-medium">Overall Progress</p>
          <span className="text-sm font-bold">{progressData.overallProgress}%</span>
        </div>
        <Progress value={progressData.overallProgress} className="h-2" />
      </div>
      
      <div className="space-y-4">
        <h3 className="text-sm font-medium text-muted-foreground">By Category</h3>
        
        {progressData.categorizedProgress.map((item, index) => (
          <div key={index}>
            <div className="flex justify-between items-center mb-1">
              <p className="text-sm">{item.category}</p>
              <span className="text-xs font-medium">{item.progress}%</span>
            </div>
            <Progress value={item.progress} className="h-1.5" />
          </div>
        ))}
      </div>
    </div>
  );
};

export default ProgressTracker;
