
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Bell, User, Settings } from 'lucide-react';

interface DashboardHeaderProps {
  userName: string;
}

const DashboardHeader = ({ userName }: DashboardHeaderProps) => {
  const navigate = useNavigate();
  
  return (
    <header className="bg-white shadow neo-border border-l-0 border-r-0 border-t-0">
      <div className="container mx-auto px-4">
        <div className="py-4 flex justify-between items-center">
          <h1 className="text-2xl font-bold">
            <a href="/" className="hover:text-primary transition-colors">
              PleaseHelpMe.Marketing
            </a>
          </h1>
          
          <div className="flex items-center gap-5">
            <button className="relative">
              <Bell size={20} />
              <span className="absolute -top-1 -right-1 h-4 w-4 bg-primary rounded-full flex items-center justify-center text-[10px] text-white">
                2
              </span>
            </button>
            
            <div className="flex items-center gap-2">
              <div className="h-8 w-8 rounded-full neo-border flex items-center justify-center">
                <User size={16} />
              </div>
              <span className="font-medium">{userName}</span>
            </div>
            
            <button>
              <Settings size={20} />
            </button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default DashboardHeader;
