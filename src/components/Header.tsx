
import React, { useState, useEffect } from 'react';
import { Menu, X } from 'lucide-react';

const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 20) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <header 
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled 
          ? 'py-3 bg-white neo-border border-t-0 border-l-0 border-r-0' 
          : 'py-5 bg-transparent'
      }`}
    >
      <div className="container mx-auto px-4 flex justify-between items-center">
        <div className="logo">
          <a href="#" className="font-bold text-xl md:text-2xl tracking-tight hover:text-primary transition-colors">
            PleaseHelpMe<span className="text-accent">.Marketing</span>
          </a>
        </div>
        
        <nav className="hidden md:block">
          <ul className="flex space-x-8">
            <li><a href="#benefits" className="hover:text-primary font-medium transition-colors">Benefits</a></li>
            <li><a href="#process" className="hover:text-primary font-medium transition-colors">Process</a></li>
            <li><a href="#testimonials" className="hover:text-primary font-medium transition-colors">Testimonials</a></li>
            <li><a href="#pricing" className="hover:text-primary font-medium transition-colors">Pricing</a></li>
          </ul>
        </nav>
        
        <div className="hidden md:block">
          <a href="#booking" className="neo-button bg-primary hover:bg-primary/90">
            Book Your Session
          </a>
        </div>
        
        <button 
          className="block md:hidden text-black"
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        >
          {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      </div>
      
      {/* Mobile menu */}
      {mobileMenuOpen && (
        <div className="md:hidden absolute top-full left-0 right-0 bg-white neo-border border-t-0 border-l-0 border-r-0 py-4 animate-fade-in">
          <div className="container mx-auto px-4">
            <ul className="space-y-4">
              <li><a href="#benefits" className="block py-2 hover:text-primary font-medium transition-colors" onClick={() => setMobileMenuOpen(false)}>Benefits</a></li>
              <li><a href="#process" className="block py-2 hover:text-primary font-medium transition-colors" onClick={() => setMobileMenuOpen(false)}>Process</a></li>
              <li><a href="#testimonials" className="block py-2 hover:text-primary font-medium transition-colors" onClick={() => setMobileMenuOpen(false)}>Testimonials</a></li>
              <li><a href="#pricing" className="block py-2 hover:text-primary font-medium transition-colors" onClick={() => setMobileMenuOpen(false)}>Pricing</a></li>
              <li>
                <a 
                  href="#booking" 
                  className="block py-2 text-center neo-button bg-primary hover:bg-primary/90" 
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Book Your Session
                </a>
              </li>
            </ul>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
