import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { testNeonConnection, createUsersTable, insertUser, getUsers } from '@/integrations/neon/test-connection';

export function NeonTest() {
  const [connectionStatus, setConnectionStatus] = useState<string>('Not tested');
  const [users, setUsers] = useState<any[]>([]);
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [loading, setLoading] = useState(false);

  const handleTestConnection = async () => {
    setLoading(true);
    const result = await testNeonConnection();
    setConnectionStatus(result.success ? '✅ Connected' : `❌ Failed: ${result.error}`);
    setLoading(false);
  };

  const handleCreateTable = async () => {
    setLoading(true);
    const result = await createUsersTable();
    if (result.success) {
      setConnectionStatus('✅ Table created');
    } else {
      setConnectionStatus(`❌ Table creation failed: ${result.error}`);
    }
    setLoading(false);
  };

  const handleInsertUser = async () => {
    if (!email || !name) return;
    
    setLoading(true);
    const result = await insertUser(email, name);
    if (result.success) {
      setEmail('');
      setName('');
      handleGetUsers(); // Refresh the user list
    } else {
      setConnectionStatus(`❌ Insert failed: ${result.error}`);
    }
    setLoading(false);
  };

  const handleGetUsers = async () => {
    setLoading(true);
    const result = await getUsers();
    if (result.success) {
      setUsers(result.users || []);
      setConnectionStatus('✅ Users loaded');
    } else {
      setConnectionStatus(`❌ Failed to load users: ${result.error}`);
    }
    setLoading(false);
  };

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle>Neon Database Test</CardTitle>
          <CardDescription>Test your Neon PostgreSQL connection</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={handleTestConnection} disabled={loading}>
              Test Connection
            </Button>
            <Button onClick={handleCreateTable} disabled={loading}>
              Create Users Table
            </Button>
            <Button onClick={handleGetUsers} disabled={loading}>
              Load Users
            </Button>
          </div>
          
          <div className="p-3 bg-gray-100 rounded">
            <strong>Status:</strong> {connectionStatus}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Add User</CardTitle>
          <CardDescription>Insert a new user into the database</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="John Doe"
              />
            </div>
          </div>
          <Button onClick={handleInsertUser} disabled={loading || !email || !name}>
            Add User
          </Button>
        </CardContent>
      </Card>

      {users.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Users ({users.length})</CardTitle>
            <CardDescription>Users in your database</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {users.map((user, index) => (
                <div key={index} className="p-3 border rounded">
                  <div><strong>ID:</strong> {user.id}</div>
                  <div><strong>Email:</strong> {user.email}</div>
                  <div><strong>Name:</strong> {user.name}</div>
                  <div><strong>Created:</strong> {new Date(user.created_at).toLocaleString()}</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
