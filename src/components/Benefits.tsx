
import React from 'react';
import { <PERSON>bulb, Target, Zap, Clock } from 'lucide-react';

const Benefits = () => {
  const benefits = [
    {
      icon: <Lightbulb size={40} />,
      title: "Expert Insights",
      description: "Get actionable advice from professionals who've seen and solved your exact marketing challenges before."
    },
    {
      icon: <Target size={40} />,
      title: "Targeted Solutions",
      description: "No generic advice. We focus on your specific situation and provide tailored recommendations."
    },
    {
      icon: <Zap size={40} />,
      title: "Quick Implementation",
      description: "Walk away with clear next steps that you can start implementing immediately."
    },
    {
      icon: <Clock size={40} />,
      title: "Fast Turnaround",
      description: "No lengthy consulting engagements. Get the guidance you need in just 30 minutes."
    }
  ];

  return (
    <section id="benefits" className="py-20 bg-muted overflow-hidden">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <span className="inline-block px-4 py-2 neo-border bg-secondary text-white font-bold mb-4 rotate-1">
            Why Choose Us
          </span>
          <h2 className="text-3xl md:text-5xl font-bold">
            Benefits of Marketing Therapy
          </h2>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {benefits.map((benefit, index) => (
            <div 
              key={index}
              className="neo-card p-6 bg-white hover:-translate-y-2 cursor-pointer"
              style={{ 
                backgroundColor: index % 4 === 0 ? '#FFFFFF' : 
                               index % 4 === 1 ? '#FFD700' : 
                               index % 4 === 2 ? '#00A3FF' : 
                               '#FF3366',
                transform: `rotate(${index % 2 === 0 ? '1' : '-1'}deg)`
              }}
            >
              <div className="mb-4 text-black">{benefit.icon}</div>
              <h3 className="text-xl font-bold mb-3">{benefit.title}</h3>
              <p>{benefit.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Benefits;
