import React, { createContext, useContext, useState, useEffect } from 'react';
import { sql } from '../integrations/neon/client';
import { toast } from '../hooks/use-toast';
import { setupApiInterceptor } from '../services/apiService';

export interface FormData {
  // Step 1: Basic Information
  firstName: string;
  lastName: string;
  email: string;
  companyName: string;
  website: string;
  
  // Step 2: Business Context
  businessType: 'startup' | 'established_business' | 'agency' | 'freelancer' | 'other' | '';
  fundingStatus: 'bootstrapped' | 'funded' | '';
  businessStage: 'early_stage' | 'growth_stage' | 'established' | 'enterprise' | '';
  industry: string;
  otherIndustry?: string;
  
  // Step 3: Marketing Specific Questions
  runningAds: 'yes' | 'no' | '';
  adsEffective?: 'yes' | 'no' | 'not_sure' | '';
  adBudget: 'less_than_5k' | '5k_10k' | '10k_20k' | '20k_50k' | '50k_100k' | '100k_plus' | '';
  marketingManagement: 'in_house' | 'agency' | 'mix' | 'not_managed' | '';
  decisionMakers: string[];
  implementationTimeline: 'right_now' | '1_3_months' | 'exploring' | '';
  marketingAreas: string[];
  
  // Step 4: Session Preparation
  marketingChallenges: string;
  sessionOutcomes: string;
  materialsToShare: string[];
  otherMaterials?: string;
  additionalInfo: string;
}

interface FormContextType {
  formData: FormData;
  updateFormData: (data: Partial<FormData>) => void;
  currentStep: number;
  setCurrentStep: (step: number) => void;
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  validateCurrentStep: () => boolean;
  errors: Record<string, string>;
  setErrors: React.Dispatch<React.SetStateAction<Record<string, string>>>;
  leadId: string | null;
}

const initialFormData: FormData = {
  // Step 1
  firstName: '',
  lastName: '',
  email: '',
  companyName: '',
  website: '',
  
  // Step 2
  businessType: '',
  fundingStatus: '',
  businessStage: '',
  industry: '',
  
  // Step 3
  runningAds: '',
  adBudget: '',
  marketingManagement: '',
  decisionMakers: [],
  implementationTimeline: '',
  marketingAreas: [],
  
  // Step 4
  marketingChallenges: '',
  sessionOutcomes: '',
  materialsToShare: [],
  additionalInfo: ''
};

const FormContext = createContext<FormContextType | undefined>(undefined);

// Type guard functions to ensure values match our expected types
const validateBusinessType = (value: string): FormData['businessType'] => {
  const validTypes: FormData['businessType'][] = ['startup', 'established_business', 'agency', 'freelancer', 'other', ''];
  return validTypes.includes(value as FormData['businessType']) 
    ? (value as FormData['businessType']) 
    : '';
};

const validateFundingStatus = (value: string): FormData['fundingStatus'] => {
  const validTypes: FormData['fundingStatus'][] = ['bootstrapped', 'funded', ''];
  return validTypes.includes(value as FormData['fundingStatus']) 
    ? (value as FormData['fundingStatus']) 
    : '';
};

const validateBusinessStage = (value: string): FormData['businessStage'] => {
  const validTypes: FormData['businessStage'][] = ['early_stage', 'growth_stage', 'established', 'enterprise', ''];
  return validTypes.includes(value as FormData['businessStage']) 
    ? (value as FormData['businessStage']) 
    : '';
};

const validateRunningAds = (value: string): FormData['runningAds'] => {
  const validTypes: FormData['runningAds'][] = ['yes', 'no', ''];
  return validTypes.includes(value as FormData['runningAds']) 
    ? (value as FormData['runningAds']) 
    : '';
};

const validateAdsEffective = (value: string): FormData['adsEffective'] => {
  const validTypes: FormData['adsEffective'][] = ['yes', 'no', 'not_sure', ''];
  return validTypes.includes(value as FormData['adsEffective']) 
    ? (value as FormData['adsEffective']) 
    : '';
};

const validateAdBudget = (value: string): FormData['adBudget'] => {
  const validTypes: FormData['adBudget'][] = ['less_than_5k', '5k_10k', '10k_20k', '20k_50k', '50k_100k', '100k_plus', ''];
  return validTypes.includes(value as FormData['adBudget']) 
    ? (value as FormData['adBudget']) 
    : '';
};

const validateMarketingManagement = (value: string): FormData['marketingManagement'] => {
  const validTypes: FormData['marketingManagement'][] = ['in_house', 'agency', 'mix', 'not_managed', ''];
  return validTypes.includes(value as FormData['marketingManagement']) 
    ? (value as FormData['marketingManagement']) 
    : '';
};

const validateImplementationTimeline = (value: string): FormData['implementationTimeline'] => {
  const validTypes: FormData['implementationTimeline'][] = ['right_now', '1_3_months', 'exploring', ''];
  return validTypes.includes(value as FormData['implementationTimeline']) 
    ? (value as FormData['implementationTimeline']) 
    : '';
};

export const FormProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [formData, setFormData] = useState<FormData>(initialFormData);
  const [currentStep, setCurrentStep] = useState(1);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [leadId, setLeadId] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Setup API interceptor on initial render
  useEffect(() => {
    setupApiInterceptor();
  }, []);

  // Load lead data from localStorage on initial render
  useEffect(() => {
    const savedLeadId = localStorage.getItem('leadId');
    if (savedLeadId) {
      setLeadId(savedLeadId);
      fetchLeadData(savedLeadId);
    }
  }, []);

  // TODO: Migrate to Neon database
  // Fetch lead data from database when leadId is available
  const fetchLeadData = async (id: string) => {
    try {
      // TODO: Replace with Neon query
      console.log('TODO: Fetch lead data from Neon database for ID:', id);
      return;

      // const data = await sql`SELECT * FROM leads WHERE id = ${id}`;
      // if (!data || data.length === 0) {
      //   console.error('Lead not found');
      //   return;
      // }

      if (data) {
        // Populate form data from database
        setFormData({
          firstName: data.first_name || '',
          lastName: data.last_name || '',
          email: data.email || '',
          companyName: data.company_name || '',
          website: data.website || '',
          businessType: validateBusinessType(data.business_type || ''),
          fundingStatus: validateFundingStatus(data.funding_status || ''),
          businessStage: validateBusinessStage(data.business_stage || ''),
          industry: data.industry || '',
          otherIndustry: data.other_industry || '',
          runningAds: validateRunningAds(data.running_ads || ''),
          adsEffective: validateAdsEffective(data.ads_effective || ''),
          adBudget: validateAdBudget(data.ad_budget || ''),
          marketingManagement: validateMarketingManagement(data.marketing_management || ''),
          decisionMakers: data.decision_makers || [],
          implementationTimeline: validateImplementationTimeline(data.implementation_timeline || ''),
          marketingAreas: data.marketing_areas || [],
          marketingChallenges: data.marketing_challenges || '',
          sessionOutcomes: data.session_outcomes || '',
          materialsToShare: data.materials_to_share || [],
          otherMaterials: data.other_materials || '',
          additionalInfo: data.additional_info || '',
        });
        
        // Set the current step from database
        setCurrentStep(data.current_step || 1);
      }
    } catch (err) {
      console.error('Error fetching lead data:', err);
    }
  };

  // Save lead data to Supabase
  const saveLeadData = async (step: number) => {
    if (isSubmitting) return;
    
    setIsSubmitting(true);
    
    try {
      // Prepare data for Supabase
      const leadData = {
        first_name: formData.firstName,
        last_name: formData.lastName,
        email: formData.email,
        company_name: formData.companyName,
        website: formData.website,
        business_type: formData.businessType,
        funding_status: formData.fundingStatus,
        business_stage: formData.businessStage,
        industry: formData.industry,
        other_industry: formData.otherIndustry,
        running_ads: formData.runningAds,
        ads_effective: formData.adsEffective,
        ad_budget: formData.adBudget,
        marketing_management: formData.marketingManagement,
        decision_makers: formData.decisionMakers,
        implementation_timeline: formData.implementationTimeline,
        marketing_areas: formData.marketingAreas,
        marketing_challenges: formData.marketingChallenges,
        session_outcomes: formData.sessionOutcomes,
        materials_to_share: formData.materialsToShare,
        other_materials: formData.otherMaterials,
        additional_info: formData.additionalInfo,
        current_step: step,
        is_completed: step === 4
      };

      let response;
      
      // TODO: Migrate to Neon database
      console.log('TODO: Save lead data to Neon database:', leadData);

      // if (leadId) {
      //   // Update existing lead
      //   await sql`UPDATE leads SET ... WHERE id = ${leadId}`;
      // } else {
      //   // Create new lead
      //   const result = await sql`INSERT INTO leads (...) VALUES (...) RETURNING id`;
      //   if (result[0]) {
      //     setLeadId(result[0].id);
      //     localStorage.setItem('leadId', result[0].id);
      //   }
      // }

      console.log('Successfully saved lead data for step', step);
    } catch (err) {
      console.error('Error saving lead data:', err);
      toast({
        title: "Error saving your progress",
        description: "We couldn't save your information. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const updateFormData = (data: Partial<FormData>) => {
    setFormData(prev => ({ ...prev, ...data }));
  };

  const goToNextStep = () => {
    if (validateCurrentStep()) {
      const nextStep = currentStep + 1;
      
      // Save data when moving to step 2 or beyond
      if (currentStep >= 1) {
        saveLeadData(nextStep);
      }
      
      setCurrentStep(nextStep);
      window.scrollTo(0, 0);
    }
  };

  const goToPreviousStep = () => {
    setCurrentStep(prev => Math.max(1, prev - 1));
    window.scrollTo(0, 0);
  };

  // Validate current step
  const validateCurrentStep = () => {
    const newErrors: Record<string, string> = {};
    let isValid = true;

    // Step 1 validation
    if (currentStep === 1) {
      if (!formData.firstName.trim()) {
        newErrors.firstName = 'First name is required';
        isValid = false;
      }
      
      if (!formData.lastName.trim()) {
        newErrors.lastName = 'Last name is required';
        isValid = false;
      }
      
      if (!formData.email.trim()) {
        newErrors.email = 'Email is required';
        isValid = false;
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
        newErrors.email = 'Please enter a valid email';
        isValid = false;
      }
      
      if (!formData.companyName.trim()) {
        newErrors.companyName = 'Company name is required';
        isValid = false;
      }
      
      if (!formData.website.trim()) {
        newErrors.website = 'Website is required';
        isValid = false;
      } else {
        try {
          new URL(formData.website.startsWith('http') ? formData.website : `https://${formData.website}`);
        } catch (e) {
          newErrors.website = 'Please enter a valid URL';
          isValid = false;
        }
      }
    }

    // Step 2 validation
    else if (currentStep === 2) {
      if (!formData.businessType) {
        newErrors.businessType = 'Please select your business type';
        isValid = false;
      }
      
      // Only validate funding status for startups
      if (formData.businessType === 'startup' && !formData.fundingStatus) {
        newErrors.fundingStatus = 'Please select your funding status';
        isValid = false;
      }
      
      if (formData.businessType === 'startup' && !formData.businessStage) {
        newErrors.businessStage = 'Please select your business stage';
        isValid = false;
      }
      
      if (!formData.industry) {
        newErrors.industry = 'Please select or enter your industry';
        isValid = false;
      }
      
      if (formData.industry === 'other' && !formData.otherIndustry?.trim()) {
        newErrors.otherIndustry = 'Please specify your industry';
        isValid = false;
      }
    }

    // Step 3 validation
    else if (currentStep === 3) {
      if (!formData.runningAds) {
        newErrors.runningAds = 'Please select an option';
        isValid = false;
      }
      
      if (formData.runningAds === 'yes' && !formData.adsEffective) {
        newErrors.adsEffective = 'Please select an option';
        isValid = false;
      }
      
      if (formData.runningAds === 'yes' && !formData.adBudget) {
        newErrors.adBudget = 'Please select your ad budget';
        isValid = false;
      }
      
      if (!formData.marketingManagement) {
        newErrors.marketingManagement = 'Please select an option';
        isValid = false;
      }
      
      if (formData.decisionMakers.length === 0) {
        newErrors.decisionMakers = 'Please select at least one option';
        isValid = false;
      }
      
      if (!formData.implementationTimeline) {
        newErrors.implementationTimeline = 'Please select a timeline';
        isValid = false;
      }
      
      if (formData.marketingAreas.length === 0) {
        newErrors.marketingAreas = 'Please select at least one area';
        isValid = false;
      }
    }

    // Step 4 validation - minimal validation as these are more open-ended
    else if (currentStep === 4) {
      if (!formData.marketingChallenges.trim()) {
        newErrors.marketingChallenges = 'Please share your marketing challenges';
        isValid = false;
      }
      
      if (!formData.sessionOutcomes.trim()) {
        newErrors.sessionOutcomes = 'Please share your desired outcomes';
        isValid = false;
      }
    }

    setErrors(newErrors);
    return isValid;
  };

  return (
    <FormContext.Provider
      value={{
        formData,
        updateFormData,
        currentStep,
        setCurrentStep,
        goToNextStep,
        goToPreviousStep,
        validateCurrentStep,
        errors,
        setErrors,
        leadId
      }}
    >
      {children}
    </FormContext.Provider>
  );
};

export const useFormContext = () => {
  const context = useContext(FormContext);
  if (context === undefined) {
    throw new Error('useFormContext must be used within a FormProvider');
  }
  return context;
};
