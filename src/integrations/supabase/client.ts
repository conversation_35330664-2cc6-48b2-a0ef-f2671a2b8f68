// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://qhakbcbmmmmtutetdbid.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFoYWtiY2JtbW1tdHV0ZXRkYmlkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzY3MTc5NDMsImV4cCI6MjA1MjI5Mzk0M30.JwZfznDj5IISxFyPfOKbTysmRjFT86Q_m2vd55OdEnc";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);