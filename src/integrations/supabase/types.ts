export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      leads: {
        Row: {
          ad_budget: string | null
          additional_info: string | null
          ads_effective: string | null
          business_stage: string | null
          business_type: string | null
          company_name: string | null
          converted_to_customer: boolean
          created_at: string
          current_step: number
          decision_makers: string[] | null
          email: string | null
          first_name: string | null
          funding_status: string | null
          id: string
          implementation_timeline: string | null
          industry: string | null
          is_completed: boolean
          last_name: string | null
          marketing_areas: string[] | null
          marketing_challenges: string | null
          marketing_management: string | null
          materials_to_share: string[] | null
          other_industry: string | null
          other_materials: string | null
          running_ads: string | null
          session_outcomes: string | null
          updated_at: string
          website: string | null
        }
        Insert: {
          ad_budget?: string | null
          additional_info?: string | null
          ads_effective?: string | null
          business_stage?: string | null
          business_type?: string | null
          company_name?: string | null
          converted_to_customer?: boolean
          created_at?: string
          current_step?: number
          decision_makers?: string[] | null
          email?: string | null
          first_name?: string | null
          funding_status?: string | null
          id?: string
          implementation_timeline?: string | null
          industry?: string | null
          is_completed?: boolean
          last_name?: string | null
          marketing_areas?: string[] | null
          marketing_challenges?: string | null
          marketing_management?: string | null
          materials_to_share?: string[] | null
          other_industry?: string | null
          other_materials?: string | null
          running_ads?: string | null
          session_outcomes?: string | null
          updated_at?: string
          website?: string | null
        }
        Update: {
          ad_budget?: string | null
          additional_info?: string | null
          ads_effective?: string | null
          business_stage?: string | null
          business_type?: string | null
          company_name?: string | null
          converted_to_customer?: boolean
          created_at?: string
          current_step?: number
          decision_makers?: string[] | null
          email?: string | null
          first_name?: string | null
          funding_status?: string | null
          id?: string
          implementation_timeline?: string | null
          industry?: string | null
          is_completed?: boolean
          last_name?: string | null
          marketing_areas?: string[] | null
          marketing_challenges?: string | null
          marketing_management?: string | null
          materials_to_share?: string[] | null
          other_industry?: string | null
          other_materials?: string | null
          running_ads?: string | null
          session_outcomes?: string | null
          updated_at?: string
          website?: string | null
        }
        Relationships: []
      }
      waitlist: {
        Row: {
          approved: boolean | null
          company: string
          created_at: string
          email: string
          email_sent: boolean | null
          help_needed: string
          id: string
        }
        Insert: {
          approved?: boolean | null
          company: string
          created_at?: string
          email: string
          email_sent?: boolean | null
          help_needed: string
          id?: string
        }
        Update: {
          approved?: boolean | null
          company?: string
          created_at?: string
          email?: string
          email_sent?: boolean | null
          help_needed?: string
          id?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type PublicSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
        PublicSchema["Views"])
    ? (PublicSchema["Tables"] &
        PublicSchema["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema["Enums"]
    ? PublicSchema["Enums"][PublicEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof PublicSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof PublicSchema["CompositeTypes"]
    ? PublicSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never
