
import React from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardHeader from '../components/dashboard/DashboardHeader';
import SessionSummary from '../components/dashboard/SessionSummary';
import ActionItems from '../components/dashboard/ActionItems';
import ResourceLibrary from '../components/dashboard/ResourceLibrary';
import ProgressTracker from '../components/dashboard/ProgressTracker';
import FollowupBooking from '../components/dashboard/FollowupBooking';

const Dashboard = () => {
  const navigate = useNavigate();
  
  // In a real app, this would come from an API or state management
  const userInfo = {
    name: '<PERSON>',
    company: 'Acme Inc.',
    sessionDate: '2023-09-15',
    topics: ['Paid Advertising', 'Growth Strategy', 'Conversion Optimization'],
    actionItems: [
      { id: '1', title: 'Review Google Ads account structure', completed: false, priority: 'high' as const },
      { id: '2', title: 'Update landing page messaging', completed: true, priority: 'high' as const },
      { id: '3', title: 'Implement UTM tracking', completed: false, priority: 'medium' as const },
      { id: '4', title: 'Review competitor sites', completed: false, priority: 'low' as const },
    ],
    resources: [
      { id: '1', title: 'Ad Campaign Structure Template', type: 'template' as const, url: '#' },
      { id: '2', title: 'Landing Page Optimization Guide', type: 'guide' as const, url: '#' },
      { id: '3', title: 'UTM Builder Tool', type: 'tool' as const, url: '#' },
    ]
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader userName={userInfo.name} />
      
      <main className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2 space-y-8">
            <SessionSummary 
              sessionDate={userInfo.sessionDate}
              company={userInfo.company}
              topics={userInfo.topics}
            />
            
            <ActionItems actionItems={userInfo.actionItems} />
            
            <ProgressTracker />
          </div>
          
          <div className="space-y-8">
            <ResourceLibrary resources={userInfo.resources} />
            
            <FollowupBooking />
          </div>
        </div>
      </main>
    </div>
  );
};

export default Dashboard;
